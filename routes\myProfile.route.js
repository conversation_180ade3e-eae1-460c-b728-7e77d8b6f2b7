const express = require('express');
const auth = require('../middlewares/auth');
const validate = require('../middlewares/validate');
const { MyProfileController } = require('../controllers');
const { MyProfileValidation } = require('../validations');
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: MyProfile
 *   description: Logged User profile management
 */
/**
 * @swagger
 * /my-profile:
 *   get:
 *     summary: Get current user profile details
 *     description: Retrieves the profile information of the currently authenticated user based on JWT token. No payload required.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: User profile retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User profile retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     identity_id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     email:
 *                       type: string
 *                       format: email
 *                       example: "<EMAIL>"
 *                     first_name:
 *                       type: string
 *                       example: "John"
 *                     last_name:
 *                       type: string
 *                       example: "Doe"
 *                     middle_name:
 *                       type: string
 *                       example: "Michael"
 *                     eid:
 *                       type: string
 *                       example: "EMP001"
 *                     identity_type:
 *                       type: integer
 *                       example: 1
 *                     identity_type_name:
 *                       type: string
 *                       example: "Employee"
 *                     national_id:
 *                       type: string
 *                       example: "123456789"
 *                     mobile:
 *                       type: string
 *                       example: "+1234567890"
 *                     start_date:
 *                       type: string
 *                       format: date
 *                       example: "2023-01-01"
 *                     end_date:
 *                       type: string
 *                       format: date
 *                       example: "2024-12-31"
 *                     status:
 *                       type: integer
 *                       example: 1
 *                     status_name:
 *                       type: string
 *                       example: "Active"
 *                     suspension:
 *                       type: boolean
 *                       example: false
 *                     suspension_date:
 *                       type: string
 *                       format: date
 *                       example: null
 *                     reason:
 *                       type: string
 *                       example: null
 *                     image:
 *                       type: string
 *                       example: "profile_image_url.jpg"
 *                     company:
 *                       type: string
 *                       example: "CareMate Inc."
 *                     organization:
 *                       type: string
 *                       example: "IT Department"
 *                     company_code:
 *                       type: string
 *                       example: "CM001"
 *                     job_title:
 *                       type: string
 *                       example: "Software Engineer"
 *                     job_code:
 *                       type: string
 *                       example: "SE001"
 *                     manager:
 *                       type: string
 *                       format: uuid
 *                       example: "456e7890-e89b-12d3-a456-************"
 *                     facility:
 *                       type: object
 *                       properties:
 *                         facility_id:
 *                           type: string
 *                           format: uuid
 *                         name:
 *                           type: string
 *                         address:
 *                           type: object
 *                           properties:
 *                             address_line_1:
 *                               type: string
 *                             address_line_2:
 *                               type: string
 *                             postal_code:
 *                               type: string
 *                             region:
 *                               type: string
 *                             country:
 *                               type: string
 *                             state:
 *                               type: string
 *                     language_preference:
 *                       type: object
 *                       properties:
 *                         language_id:
 *                           type: string
 *                         name:
 *                           type: string
 *                         code:
 *                           type: string
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: User profile not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User profile not found"
 */
router.get(
  "/",
  auth(),
  MyProfileController.getCurrentUserProfile
);

/**
 * @swagger
 * /my-profile/cards:
 *   get:
 *     summary: Get current user's cards
 *     description: Retrieves all cards associated with the currently authenticated user based on JWT token. No payload required.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: User cards retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User cards retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       card_id:
 *                         type: string
 *                         format: uuid
 *                         example: "123e4567-e89b-12d3-a456-************"
 *                         description: Unique identifier for the card
 *                       card_number:
 *                         type: string
 *                         example: "1234567890"
 *                         description: The card number
 *                       card_format:
 *                         type: integer
 *                         example: 1
 *                         description: Format type of the card
 *                       card_format_name:
 *                         type: string
 *                         example: "Proximity Card"
 *                         description: Human readable name for card format
 *                       facility_code:
 *                         type: integer
 *                         example: 123
 *                         description: Facility code associated with the card
 *                       pin:
 *                         type: integer
 *                         example: 1234
 *                         description: PIN number for the card
 *                       template:
 *                         type: integer
 *                         example: 1
 *                         description: Template type for the card
 *                       card_template_name:
 *                         type: string
 *                         example: "Standard Template"
 *                         description: Human readable name for card template
 *                       active_date:
 *                         type: string
 *                         format: date
 *                         example: "2023-01-01"
 *                         description: Date when the card becomes active
 *                       deactive_date:
 *                         type: string
 *                         format: date
 *                         example: "2024-12-31"
 *                         description: Date when the card becomes inactive
 *                       reason:
 *                         type: string
 *                         example: "Employee card"
 *                         description: Reason for card issuance
 *                       status:
 *                         type: integer
 *                         example: 1
 *                         description: Current status of the card
 *                       card_status_name:
 *                         type: string
 *                         example: "Active"
 *                         description: Human readable name for card status
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Card creation timestamp
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Card last update timestamp
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: No cards found for the user
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No cards found for the user"
 */
router.get(
    "/cards",
    auth(),
    MyProfileController.getCurrentUserCard
)

/**
 * @swagger
 * /my-profile/access:
 *   get:
 *     summary: Get current user's access records
 *     description: Retrieves all access records for the currently authenticated user including area names, start dates, end dates, and status. No payload required.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: User access records retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User access records retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       identity_access_id:
 *                         type: string
 *                         format: uuid
 *                         example: "123e4567-e89b-12d3-a456-************"
 *                         description: Unique identifier for the access record
 *                       area_name:
 *                         type: string
 *                         example: "Main Building Lobby"
 *                         description: Name of the access area (from pacs_area_name or access level name)
 *                       access_level_name:
 *                         type: string
 *                         example: "Employee Access Level"
 *                         description: Name of the access level
 *                       description:
 *                         type: string
 *                         example: "Standard employee access to main building areas"
 *                         description: Description of the access level
 *                       access_level_type:
 *                         type: integer
 *                         example: 1
 *                         description: Type of access level
 *                       access_level_type_name:
 *                         type: string
 *                         example: "Employee"
 *                         description: Human readable name for access level type
 *                       start_date:
 *                         type: string
 *                         format: date
 *                         example: "2023-01-01"
 *                         description: Date when access becomes active
 *                       end_date:
 *                         type: string
 *                         format: date
 *                         example: "2024-12-31"
 *                         description: Date when access expires
 *                       status:
 *                         type: integer
 *                         example: 1
 *                         description: Current status of the access record
 *                       status_name:
 *                         type: string
 *                         example: "Active"
 *                         description: Human readable name for access status
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Access record creation timestamp
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Access record last update timestamp
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: No access records found for the user
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No access records found for the user"
 */
router.get(
    "/access",
    auth(),
    MyProfileController.getCurrentUserAccess
)

/**
 * @swagger
 * /my-profile/vehicles:
 *   get:
 *     summary: Get current user's vehicles
 *     description: Retrieves all vehicles registered to the currently authenticated user based on JWT token. No payload required.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: User vehicles retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User vehicles retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       vehicle_id:
 *                         type: string
 *                         format: uuid
 *                         example: "123e4567-e89b-12d3-a456-************"
 *                         description: Unique identifier for the vehicle
 *                       plate_number:
 *                         type: string
 *                         example: "ABC-1234"
 *                         description: Vehicle license plate number
 *                       issued_by:
 *                         type: string
 *                         example: "Department of Motor Vehicles"
 *                         description: Authority that issued the plate
 *                       vin:
 *                         type: string
 *                         example: "1HGBH41JXMN109186"
 *                         description: Vehicle Identification Number (17 characters)
 *                       year:
 *                         type: integer
 *                         example: 2020
 *                         description: Manufacturing year of the vehicle
 *                       make:
 *                         type: string
 *                         example: "Toyota"
 *                         description: Vehicle manufacturer
 *                       model:
 *                         type: string
 *                         example: "Camry"
 *                         description: Vehicle model
 *                       color:
 *                         type: string
 *                         example: "Blue"
 *                         description: Vehicle color
 *                       uploaded_date:
 *                         type: string
 *                         format: date
 *                         example: "2023-01-15"
 *                         description: Date when vehicle information was uploaded
 *                       created_by:
 *                         type: string
 *                         format: uuid
 *                         example: "456e7890-e89b-12d3-a456-************"
 *                         description: ID of user who created the vehicle record
 *                       updated_by:
 *                         type: string
 *                         format: uuid
 *                         example: "456e7890-e89b-12d3-a456-************"
 *                         description: ID of user who last updated the vehicle record
 *                       created_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Vehicle record creation timestamp
 *                       updated_at:
 *                         type: string
 *                         format: date-time
 *                         example: "2023-01-01T00:00:00.000Z"
 *                         description: Vehicle record last update timestamp
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: No vehicles found for the user
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "No vehicles found for the user"
 */
router.get(
    "/vehicles",
    auth(),
    MyProfileController.getCurrentUserVehicles
)

/**
 * @swagger
 * /my-profile/vehicle:
 *   post:
 *     summary: Add a new vehicle for the current user
 *     description: Creates a new vehicle record for the currently authenticated user. The vehicle will be automatically associated with the user's identity from the JWT token.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - plate_number
 *             properties:
 *               plate_number:
 *                 type: string
 *                 maxLength: 20
 *                 example: "ABC123"
 *                 description: Vehicle license plate number (must be unique)
 *               issued_by:
 *                 type: string
 *                 maxLength: 100
 *                 example: "DMV California"
 *                 description: Authority that issued the license plate
 *               vin:
 *                 type: string
 *                 minLength: 17
 *                 maxLength: 17
 *                 example: "1HGBH41JXMN109186"
 *                 description: Vehicle Identification Number (exactly 17 characters)
 *               year:
 *                 type: integer
 *                 minimum: 1900
 *                 maximum: 2026
 *                 example: 2022
 *                 description: Manufacturing year of the vehicle
 *               make:
 *                 type: string
 *                 maxLength: 100
 *                 example: "Toyota"
 *                 description: Vehicle manufacturer
 *               model:
 *                 type: string
 *                 maxLength: 100
 *                 example: "Camry"
 *                 description: Vehicle model
 *               color:
 *                 type: string
 *                 maxLength: 50
 *                 example: "Blue"
 *                 description: Vehicle color
 *               uploaded_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-15"
 *                 description: Date when vehicle information was uploaded
 *           examples:
 *             complete_vehicle:
 *               summary: Complete vehicle information
 *               value:
 *                 plate_number: "ABC123"
 *                 issued_by: "DMV California"
 *                 vin: "1HGBH41JXMN109186"
 *                 year: 2022
 *                 make: "Toyota"
 *                 model: "Camry"
 *                 color: "Blue"
 *                 uploaded_date: "2024-01-15"
 *             minimal_vehicle:
 *               summary: Minimal required information
 *               value:
 *                 plate_number: "XYZ789"
 *     responses:
 *       201:
 *         description: Vehicle created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Vehicle created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     vehicle_id:
 *                       type: string
 *                       format: uuid
 *                       example: "123e4567-e89b-12d3-a456-************"
 *                     plate_number:
 *                       type: string
 *                       example: "ABC123"
 *                     issued_by:
 *                       type: string
 *                       example: "DMV California"
 *                     vin:
 *                       type: string
 *                       example: "1HGBH41JXMN109186"
 *                     year:
 *                       type: integer
 *                       example: 2022
 *                     make:
 *                       type: string
 *                       example: "Toyota"
 *                     model:
 *                       type: string
 *                       example: "Camry"
 *                     color:
 *                       type: string
 *                       example: "Blue"
 *                     uploaded_date:
 *                       type: string
 *                       format: date
 *                       example: "2024-01-15"
 *                     created_by:
 *                       type: string
 *                       format: uuid
 *                       example: "456e7890-e89b-12d3-a456-************"
 *                     updated_by:
 *                       type: string
 *                       format: uuid
 *                       example: "456e7890-e89b-12d3-a456-************"
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-15T10:30:00.000Z"
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-15T10:30:00.000Z"
 *       400:
 *         description: Bad Request - Validation errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Validation error"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                         example: "plate_number"
 *                       message:
 *                         type: string
 *                         example: "plate_number is required"
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       409:
 *         description: Conflict - Vehicle already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Vehicle with this plate number already exists"
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Failed to create vehicle"
 */
router.post(
    "/vehicle",
    auth(),
    validate(MyProfileValidation.createVehicle),
    MyProfileController.createVehicle
)

/**
 * @swagger
 * /my-profile/delegates:
 *   get:
 *     summary: Get current user's delegation information
 *     description: Retrieves two types of delegations for the currently authenticated user - delegations assigned to the user (where they are the delegate) and delegations created by the user (their delegates). No payload required.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: User delegates retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "User delegates retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     delegates_assigned_to_me:
 *                       type: object
 *                       description: Delegations where the current user is the delegate
 *                       properties:
 *                         count:
 *                           type: integer
 *                           example: 2
 *                           description: Number of delegations assigned to the user
 *                         data:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               delegate_id:
 *                                 type: string
 *                                 format: uuid
 *                                 example: "123e4567-e89b-12d3-a456-************"
 *                               name:
 *                                 type: string
 *                                 example: "John Doe"
 *                                 description: Name of the delegate
 *                               eid:
 *                                 type: string
 *                                 example: "EMP001"
 *                                 description: Employee ID of the delegate
 *                               task_to_delegate:
 *                                 type: string
 *                                 example: "Review and approve expense reports"
 *                                 description: Description of the delegated task
 *                               start_date:
 *                                 type: string
 *                                 format: date
 *                                 example: "2024-01-01"
 *                                 description: Start date of the delegation
 *                               end_date:
 *                                 type: string
 *                                 format: date
 *                                 example: "2024-03-31"
 *                                 description: End date of the delegation (can be null)
 *                               status:
 *                                 type: integer
 *                                 example: 1
 *                                 description: Status of the delegation (references master_data)
 *                               status_name:
 *                                 type: string
 *                                 example: "Active"
 *                                 description: Human-readable status name
 *                               delegated_by:
 *                                 type: object
 *                                 properties:
 *                                   identity_id:
 *                                     type: string
 *                                     format: uuid
 *                                     example: "456e7890-e89b-12d3-a456-************"
 *                                     description: ID of the person who created this delegation
 *                               created_at:
 *                                 type: string
 *                                 format: date-time
 *                                 example: "2024-01-01T10:00:00.000Z"
 *                               updated_at:
 *                                 type: string
 *                                 format: date-time
 *                                 example: "2024-01-01T10:00:00.000Z"
 *                     my_delegates:
 *                       type: object
 *                       description: Delegations created by the current user
 *                       properties:
 *                         count:
 *                           type: integer
 *                           example: 3
 *                           description: Number of delegations created by the user
 *                         data:
 *                           type: array
 *                           items:
 *                             type: object
 *                             properties:
 *                               delegate_id:
 *                                 type: string
 *                                 format: uuid
 *                                 example: "789e1234-e89b-12d3-a456-************"
 *                               name:
 *                                 type: string
 *                                 example: "Jane Smith"
 *                                 description: Name of the delegate
 *                               eid:
 *                                 type: string
 *                                 example: "EMP002"
 *                                 description: Employee ID of the delegate
 *                               task_to_delegate:
 *                                 type: string
 *                                 example: "Handle customer support tickets"
 *                                 description: Description of the delegated task
 *                               start_date:
 *                                 type: string
 *                                 format: date
 *                                 example: "2024-02-01"
 *                               end_date:
 *                                 type: string
 *                                 format: date
 *                                 example: "2024-02-28"
 *                               status:
 *                                 type: integer
 *                                 example: 1
 *                               status_name:
 *                                 type: string
 *                                 example: "Active"
 *                               delegated_to:
 *                                 type: object
 *                                 properties:
 *                                   identity_id:
 *                                     type: string
 *                                     format: uuid
 *                                     example: "abc12345-e89b-12d3-a456-************"
 *                                   first_name:
 *                                     type: string
 *                                     example: "Jane"
 *                                   last_name:
 *                                     type: string
 *                                     example: "Smith"
 *                                   email:
 *                                     type: string
 *                                     example: "<EMAIL>"
 *                                   eid:
 *                                     type: string
 *                                     example: "EMP002"
 *                               created_at:
 *                                 type: string
 *                                 format: date-time
 *                                 example: "2024-02-01T09:00:00.000Z"
 *                               updated_at:
 *                                 type: string
 *                                 format: date-time
 *                                 example: "2024-02-01T09:00:00.000Z"
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Failed to fetch delegates"
 */
router.get(
    "/delegates",
    auth(),
    MyProfileController.getCurrentUserDelegates
)

/**
 * @swagger
 * /my-profile/delegation:
 *   post:
 *     summary: Create a new delegation for another user
 *     description: Creates a new delegation where the current user delegates a task to another user. The current user becomes the creator of the delegation.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - identity_id
 *               - task_to_delegate
 *               - start_date
 *             properties:
 *               identity_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *                 description: UUID of the user to delegate the task to
 *               task_to_delegate:
 *                 type: string
 *                 maxLength: 1000
 *                 example: "Review and approve expense reports for Q1 2024"
 *                 description: Description of the task being delegated
 *               start_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-01"
 *                 description: Start date of the delegation
 *               end_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-03-31"
 *                 description: End date of the delegation (optional)
 *               name:
 *                 type: string
 *                 maxLength: 200
 *                 example: "John Doe"
 *                 description: Name of the delegate (optional, will auto-fill from identity if not provided)
 *               eid:
 *                 type: string
 *                 maxLength: 50
 *                 example: "EMP001"
 *                 description: Employee ID of the delegate (optional, will auto-fill from identity if not provided)
 *           examples:
 *             complete_delegation:
 *               summary: Complete delegation information
 *               value:
 *                 identity_id: "123e4567-e89b-12d3-a456-************"
 *                 task_to_delegate: "Review and approve expense reports for Q1 2024"
 *                 start_date: "2024-01-01"
 *                 end_date: "2024-03-31"
 *                 name: "John Doe"
 *                 eid: "EMP001"
 *             minimal_delegation:
 *               summary: Minimal required information
 *               value:
 *                 identity_id: "123e4567-e89b-12d3-a456-************"
 *                 task_to_delegate: "Handle customer support tickets"
 *                 start_date: "2024-02-01"
 *     responses:
 *       201:
 *         description: Delegation created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Delegation created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     delegate_id:
 *                       type: string
 *                       format: uuid
 *                       example: "789e1234-e89b-12d3-a456-************"
 *                     name:
 *                       type: string
 *                       example: "John Doe"
 *                     eid:
 *                       type: string
 *                       example: "EMP001"
 *                     task_to_delegate:
 *                       type: string
 *                       example: "Review and approve expense reports for Q1 2024"
 *                     start_date:
 *                       type: string
 *                       format: date
 *                       example: "2024-01-01"
 *                     end_date:
 *                       type: string
 *                       format: date
 *                       example: "2024-03-31"
 *                     status:
 *                       type: integer
 *                       example: 1
 *                     status_name:
 *                       type: string
 *                       example: "Active"
 *                     delegated_to:
 *                       type: object
 *                       properties:
 *                         identity_id:
 *                           type: string
 *                           format: uuid
 *                           example: "123e4567-e89b-12d3-a456-************"
 *                         first_name:
 *                           type: string
 *                           example: "John"
 *                         last_name:
 *                           type: string
 *                           example: "Doe"
 *                         email:
 *                           type: string
 *                           example: "<EMAIL>"
 *                         eid:
 *                           type: string
 *                           example: "EMP001"
 *                     created_by:
 *                       type: string
 *                       format: uuid
 *                       example: "456e7890-e89b-12d3-a456-************"
 *                     updated_by:
 *                       type: string
 *                       format: uuid
 *                       example: "456e7890-e89b-12d3-a456-************"
 *                     created_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-01T10:00:00.000Z"
 *                     updated_at:
 *                       type: string
 *                       format: date-time
 *                       example: "2024-01-01T10:00:00.000Z"
 *       400:
 *         description: Bad Request - Validation errors or business logic errors
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Cannot delegate to yourself"
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: Not Found - Target identity does not exist
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Target identity not found"
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Failed to create delegation"
 */
router.post(
    "/delegation",
    auth(),
    validate(MyProfileValidation.createDelegation),
    MyProfileController.createDelegation
)

/**
 * @swagger
 * /my-profile/corporate:
 *   get:
 *     summary: Get corporate information for the current user
 *     description: Retrieves corporate information including hiring organization, corporate identity, facility information, and address details for the currently authenticated user.
 *     security:
 *       - bearerAuth: []
 *     tags: [MyProfile]
 *     responses:
 *       200:
 *         description: Corporate information retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Corporate information retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     hiring_organization:
 *                       type: object
 *                       properties:
 *                         type:
 *                           type: string
 *                           example: "Employee"
 *                           description: Type of identity (Employee, Contractor, etc.)
 *                         status:
 *                           type: string
 *                           example: "ACTIVE"
 *                           description: Current status of the user
 *                         hiring_company:
 *                           type: string
 *                           example: "ORACLE AMERICA, INC."
 *                           description: Company that hired the user
 *                         hiring_organization:
 *                           type: string
 *                           example: "ORCL US"
 *                           description: Organization within the company
 *                         start_date:
 *                           type: string
 *                           format: date
 *                           example: "2021-01-11"
 *                           description: Start date of employment
 *                         legacy_cost_center:
 *                           type: string
 *                           example: "6EP2 - OCI Physical Security"
 *                           description: Legacy cost center information
 *                     corporate_identity:
 *                       type: object
 *                       properties:
 *                         hcmid:
 *                           type: string
 *                           example: "300016491970"
 *                           description: Human Capital Management ID
 *                         user_name:
 *                           type: string
 *                           example: "<EMAIL>"
 *                           description: User's email/username
 *                         oracle_id:
 *                           type: string
 *                           example: "*********"
 *                           description: Oracle ID or company code
 *                         manager_sponsor:
 *                           type: string
 *                           example: "Steven Kruschke *********"
 *                           description: Manager or sponsor information
 *                     facility_information:
 *                       type: object
 *                       properties:
 *                         facility:
 *                           type: string
 *                           example: "Santa Clara"
 *                           description: Facility name where user is located
 *                     address:
 *                       type: object
 *                       properties:
 *                         work_address_line1:
 *                           type: string
 *                           example: "4030 George Sellon Circle"
 *                           description: Primary work address line
 *                         work_address_line2:
 *                           type: string
 *                           example: ""
 *                           description: Secondary work address line
 *                         country:
 *                           type: string
 *                           example: "United States"
 *                           description: Country of work location
 *                         state:
 *                           type: string
 *                           example: "CA"
 *                           description: State or province of work location
 *                         city:
 *                           type: string
 *                           example: "Santa Clara"
 *                           description: City of work location
 *                         postal_code:
 *                           type: string
 *                           example: "95054"
 *                           description: Postal code of work location
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       404:
 *         description: User not found
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User not found"
 */
router.get(
    "/corporate",
    auth(),
    MyProfileController.getCurrentUserCorporate
)

/**
 * @swagger
 * /my-profile/card-request:
 *   post:
 *     summary: Create a card request for current user with optional shipping details
 *     description: Creates a new card request for the authenticated user. If shipping is required (shipping_required = 1), shipping details must be provided and will be stored in the card_shipping table. Both card request and shipping data are managed through this single endpoint.
 *     tags: [MyProfile]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - card_number
 *               - card_format
 *               - template
 *               - shipping_required
 *               - status
 *             properties:
 *               # Card Request Fields
 *               card_number:
 *                 type: string
 *                 maxLength: 50
 *                 example: "1234567890"
 *                 description: Unique card number for the request
 *               card_format:
 *                 type: integer
 *                 example: 1
 *                 description: Format type of the card (references master_data)
 *               facility_code:
 *                 type: integer
 *                 example: 123
 *                 description: Optional facility code for the card
 *               pin:
 *                 type: integer
 *                 example: 1234
 *                 description: Optional PIN number for the card
 *               template:
 *                 type: integer
 *                 example: 1
 *                 description: Card template type (references master_data card_template group)
 *               active_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-01-01"
 *                 description: Date when the card becomes active (optional, can be empty string)
 *               deactive_date:
 *                 type: string
 *                 format: date
 *                 example: "2024-12-31"
 *                 description: Date when the card becomes inactive (optional, can be empty string)
 *               reason:
 *                 type: string
 *                 example: "New employee card request"
 *                 description: Reason for the card request
 *               shipping_required:
 *                 type: integer
 *                 enum: [0, 1]
 *                 example: 1
 *                 description: Whether shipping is required (0 = No, 1 = Yes). References master_data shipping_required group
 *               status:
 *                 type: integer
 *                 example: 1
 *                 description: Status of the card request (references master_data card_status group)
 *               pick_up_facility_id:
 *                 type: string
 *                 format: uuid
 *                 example: "123e4567-e89b-12d3-a456-************"
 *                 description: Optional facility ID for card pickup
 *
 *               # Shipping Fields (Required when shipping_required = 1)
 *               ship_to:
 *                 type: string
 *                 example: "Home"
 *                 description: Shipping destination type (required when shipping_required = 1)
 *               ship_to_name:
 *                 type: string
 *                 example: "John Doe"
 *                 description: Name of the person to ship to (required when shipping_required = 1)
 *               address_line_1:
 *                 type: string
 *                 example: "123 Main Street"
 *                 description: Primary shipping address line (required when shipping_required = 1)
 *               address_line_2:
 *                 type: string
 *                 example: "Apt 4B"
 *                 description: Secondary shipping address line (optional)
 *               country_id:
 *                 type: string
 *                 format: uuid
 *                 example: "456e7890-e89b-12d3-a456-************"
 *                 description: Country UUID for shipping address (required when shipping_required = 1)
 *               state_id:
 *                 type: string
 *                 format: uuid
 *                 example: "789e1234-e89b-12d3-a456-************"
 *                 description: State UUID for shipping address (required when shipping_required = 1)
 *               zip_code:
 *                 type: string
 *                 example: "12345"
 *                 description: Postal/ZIP code for shipping address (required when shipping_required = 1)
 *               mobile_phone:
 *                 type: string
 *                 example: "+1234567890"
 *                 description: Mobile phone number for shipping contact (required when shipping_required = 1)
 *           examples:
 *             with_shipping:
 *               summary: Card request with shipping
 *               value:
 *                 card_number: "1234567890"
 *                 card_format: 1
 *                 facility_code: 123
 *                 pin: 1234
 *                 template: 1
 *                 active_date: "2024-01-01"
 *                 deactive_date: "2024-12-31"
 *                 reason: "New employee card"
 *                 shipping_required: 1
 *                 status: 1
 *                 pick_up_facility_id: ""
 *                 ship_to: "Home"
 *                 ship_to_name: "John Doe"
 *                 address_line_1: "123 Main Street"
 *                 address_line_2: "Apt 4B"
 *                 country_id: "456e7890-e89b-12d3-a456-************"
 *                 state_id: "789e1234-e89b-12d3-a456-************"
 *                 zip_code: "12345"
 *                 mobile_phone: "+1234567890"
 *             without_shipping:
 *               summary: Card request without shipping (pickup)
 *               value:
 *                 card_number: "0987654321"
 *                 card_format: 1
 *                 facility_code: 456
 *                 pin: 5678
 *                 template: 1
 *                 active_date: "2024-01-01"
 *                 deactive_date: ""
 *                 reason: "Replacement card"
 *                 shipping_required: 0
 *                 status: 1
 *                 pick_up_facility_id: "123e4567-e89b-12d3-a456-************"
 *     responses:
 *       201:
 *         description: Card request created successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Card request created successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     card_request:
 *                       type: object
 *                       properties:
 *                         card_request_id:
 *                           type: string
 *                           format: uuid
 *                           example: "123e4567-e89b-12d3-a456-************"
 *                         identity_id:
 *                           type: string
 *                           format: uuid
 *                           example: "456e7890-e89b-12d3-a456-************"
 *                         card_number:
 *                           type: string
 *                           example: "1234567890"
 *                         card_format:
 *                           type: integer
 *                           example: 1
 *                         facility_code:
 *                           type: integer
 *                           example: 123
 *                         pin:
 *                           type: integer
 *                           example: 1234
 *                         template:
 *                           type: integer
 *                           example: 1
 *                         active_date:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-01-01T00:00:00.000Z"
 *                         deactive_date:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-12-31T00:00:00.000Z"
 *                         reason:
 *                           type: string
 *                           example: "New employee card"
 *                         shipping_required:
 *                           type: integer
 *                           example: 1
 *                         status:
 *                           type: integer
 *                           example: 1
 *                         pick_up_facility_id:
 *                           type: string
 *                           format: uuid
 *                           example: null
 *                         updated_by:
 *                           type: string
 *                           format: uuid
 *                           example: "456e7890-e89b-12d3-a456-************"
 *                         created_at:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-01-01T12:00:00.000Z"
 *                         updated_at:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-01-01T12:00:00.000Z"
 *                     shipping_details:
 *                       type: object
 *                       description: Only present when shipping_required = 1
 *                       properties:
 *                         card_shipping_id:
 *                           type: string
 *                           format: uuid
 *                           example: "789e1234-e89b-12d3-a456-************"
 *                         card_request_id:
 *                           type: string
 *                           format: uuid
 *                           example: "123e4567-e89b-12d3-a456-************"
 *                         ship_to:
 *                           type: string
 *                           example: "Home"
 *                         ship_to_name:
 *                           type: string
 *                           example: "John Doe"
 *                         address_line_1:
 *                           type: string
 *                           example: "123 Main Street"
 *                         address_line_2:
 *                           type: string
 *                           example: "Apt 4B"
 *                         country_id:
 *                           type: string
 *                           format: uuid
 *                           example: "456e7890-e89b-12d3-a456-************"
 *                         state_id:
 *                           type: string
 *                           format: uuid
 *                           example: "789e1234-e89b-12d3-a456-************"
 *                         zip_code:
 *                           type: string
 *                           example: "12345"
 *                         mobile_phone:
 *                           type: string
 *                           example: "+1234567890"
 *                         updated_by:
 *                           type: string
 *                           format: uuid
 *                           example: "456e7890-e89b-12d3-a456-************"
 *                         created_at:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-01-01T12:00:00.000Z"
 *                         updated_at:
 *                           type: string
 *                           format: date-time
 *                           example: "2024-01-01T12:00:00.000Z"
 *       400:
 *         description: Bad Request - Validation errors or invalid input
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Validation error"
 *                 errors:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       field:
 *                         type: string
 *                         example: "card_number"
 *                       message:
 *                         type: string
 *                         example: "card_number is required"
 *       401:
 *         description: Unauthorized - Invalid or missing JWT token
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "User identity not found in token"
 *       409:
 *         description: Conflict - Card number already exists
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Card number already exists"
 *       500:
 *         description: Internal Server Error
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 success:
 *                   type: boolean
 *                   example: false
 *                 message:
 *                   type: string
 *                   example: "Internal server error"
 */
router.post(
    "/card-request",
    auth(),
    validate(MyProfileValidation.createCardRequest),
    MyProfileController.createCardRequest
);

module.exports = router;