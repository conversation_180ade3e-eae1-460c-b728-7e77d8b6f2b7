const { Visit, Guest, GuestVisit, VisitG<PERSON><PERSON><PERSON><PERSON>n, sequelize, VisitGuestView } = require("../models");
const httpStatus = require("http-status");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { generateOTP } = require("../helpers/global.helper");
const { paginate } = require("../models/plugins/paginate.plugin");
const { Op } = require("sequelize");
/**
 * @desc Create a visit, guest, and guest_visit in a single transaction
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing the created visit, guest, and guest_visit
 */
exports.createVisitWithGuest = catchAsync(async (req, res) => {
  const t = await sequelize.transaction();
  try {
    const {
      facility_id,
      host_id,
      escort_id,
      start_date,
      start_time,
      duration,
      guests, // Accept array of guests
      updated_by
    } = req.body;

    // 1. Create Visit
    const visit = await Visit.create({
      facility_id,
      host_id,
      escort_id,
      start_date,
      start_time,
      duration,
      type: 1, // set type as 1
      updated_by
    }, { transaction: t });

    // 2. Create multiple Guests (only for new guests)
    const newGuestsPayload = guests
      .filter(g => !g.guest_id)
      .map(guest => ({
        first_name: guest.first_name,
        last_name: guest.last_name,
        date_of_birth: guest.date_of_birth,
        email: guest.email,
        mobile_phone: guest.mobile_phone,
        image: guest.image,
        updated_by
      }));

    const existingGuests = guests //separate existing guests
      .filter(g => g.guest_id)
      .map(guest => ({
        guest_id: guest.guest_id
      }));

    console.log('::::::::: existingGuests ', existingGuests);
    const createdGuests = await Guest.bulkCreate(newGuestsPayload, {
      transaction: t,
      returning: true
    });

    // 3. Create multiple GuestVisit records
    const guestsForVisit = [...createdGuests, ...existingGuests];
    const guestVisitPayload = guestsForVisit.map(guest => ({
      guest_id: guest.guest_id,
      visit_id: visit.visit_id
    }));

    const guestVisits = await GuestVisit.bulkCreate(guestVisitPayload, {
      transaction: t,
      returning: true
    });

    await t.commit();
    sendSuccess(res, "Visit and Guests created successfully", httpStatus.CREATED, {
      visit,
      guests: guestsForVisit,
      guestVisits
    });
  } catch (error) {
    await t.rollback();
    sendError(res, error.message || "Failed to create visit with guests", httpStatus.BAD_REQUEST);
  }
});

/**
 * @desc Create a visit with extra fields and optionally link an existing guest via guest_id in guest_visit
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing the created visit and guestVisit (if guest_id provided)
 */
exports.createEventVisit = catchAsync(async (req, res) => {
  const t = await sequelize.transaction();
  try {
    const {
      title,
      category,
      type = 0,
      start_date,
      end_date,
      repeat_visit,
      facility_id,
      access_level_id,
      host_id,
      check_in_instruction,
      escort_id,
      send_notification,
      remind_me,
      message_to_visitor,
      guest_ids = [], // Accept array of guest IDs
      status,
      updated_by
    } = req.body;

    // 1. Create Visit with type 0
    const visit = await Visit.create({
      title,
      type: 0,
      category,
      start_date,
      end_date,
      repeat_visit,
      facility_id,
      access_level_id,
      host_id,
      check_in_instruction,
      escort_id,
      send_notification,
      remind_me,
      message_to_visitor,
      status,
      updated_by
    }, { transaction: t });

    let guestVisits = [];

    if (guest_ids.length > 0) {
      // 2. Create multiple GuestVisit records
      const guestVisitPayload = guest_ids.map(guest_id => ({
        guest_id,
        visit_id: visit.visit_id
      }));

      guestVisits = await GuestVisit.bulkCreate(guestVisitPayload, { transaction: t });
    }

    await t.commit();
    sendSuccess(res, "Event Visit created successfully", httpStatus.CREATED, {
      visit,
      guestVisits
    });
  } catch (error) {
    await t.rollback();
    sendError(res, error.message || "Failed to create event visit", httpStatus.BAD_REQUEST);
  }
});


/**
 * @desc Get visit summary with host/escort names
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing visit summary
 */
exports.getVisitSummary = catchAsync(async (req, res) => {
  const { visit_id } = req.params;
  const visit = await Visit.findByPk(visit_id, {
    attributes: [
      "title",
      "category",
      "type",
      "host_id",
      "escort_id",
      "start_date",
      "end_date",
      "status"
    ],
    include: [
      {
        model: require("../models").Identity,
        as: "host",
        attributes: [
          [sequelize.literal(`CONCAT("host"."first_name", ' ', "host"."last_name")`), "host_name"]
        ],
      },
      {
        model: require("../models").Identity,
        as: "escort",
        attributes: [
          [sequelize.literal(`CONCAT("escort"."first_name", ' ', "escort"."last_name")`), "escort_name"]
        ],
      }
    ]
  });
  if (!visit) {
    return sendError(res, "Visit not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Visit summary retrieved successfully", httpStatus.OK, visit);
});

/**
 * @desc Update an existing visit
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing the updated visit
 */
exports.updateVisit = catchAsync(async (req, res) => {
  const { visit_id } = req.params;
  const t = await sequelize.transaction();

  try {
    // Check if visit exists
    const existingVisit = await Visit.findByPk(visit_id, { transaction: t });
    if (!existingVisit) {
      await t.rollback();
      return sendError(res, "Visit not found", httpStatus.NOT_FOUND);
    }

    const {
      title,
      category,
      type,
      start_date,
      end_date,
      repeat_visit,
      facility_id,
      access_level_id,
      host_id,
      check_in_instruction,
      escort_id,
      send_notification,
      remind_me,
      message_to_visitor,
      status,
      updated_by,
      guest_ids = [] // Array of guest IDs to associate with this visit
    } = req.body;

    // Update visit
    const updatedVisit = await existingVisit.update({
      title,
      type,
      category,
      start_date,
      end_date,
      repeat_visit,
      facility_id,
      access_level_id,
      host_id,
      check_in_instruction,
      escort_id,
      send_notification,
      remind_me,
      message_to_visitor: message_to_visitor || existingVisit.visitor_message,
      status,
      updated_by
    }, { transaction: t });

    // Handle guest associations update if guest_ids is provided
    if (guest_ids && guest_ids.length >= 0) {
      // First, delete all existing guest visits for this visit
      await GuestVisit.destroy({
        where: { visit_id },
        transaction: t
      });

      // Then, create new guest visits if guest_ids are provided
      if (guest_ids.length > 0) {
        const guestVisitPayload = guest_ids.map(guest_id => ({
          guest_id,
          visit_id: visit_id
        }));

        await GuestVisit.bulkCreate(guestVisitPayload, { transaction: t });
      }
    }

    await t.commit();
    sendSuccess(res, "Visit updated successfully", httpStatus.OK, {
      visit: updatedVisit,
      message: guest_ids && guest_ids.length >= 0 ?
        `Visit updated with ${guest_ids.length} guest associations` :
        "Visit updated successfully"
    });
  } catch (error) {
    await t.rollback();
    sendError(res, error.message || "Failed to update visit", httpStatus.BAD_REQUEST);
  }
});

/**
 * @desc Delete a visit and its associated guest visits
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object confirming deletion
 */
exports.deleteVisit = catchAsync(async (req, res) => {
  const { visit_id } = req.params;
  const t = await sequelize.transaction();

  try {
    // Check if visit exists
    const existingVisit = await Visit.findByPk(visit_id, { transaction: t });
    if (!existingVisit) {
      await t.rollback();
      return sendError(res, "Visit not found", httpStatus.NOT_FOUND);
    }

    // Delete associated guest visits first (due to foreign key constraints)
    await GuestVisit.destroy({
      where: { visit_id },
      transaction: t
    });

    // Delete the visit
    await existingVisit.destroy({ transaction: t });

    await t.commit();
    sendSuccess(res, "Visit deleted successfully", httpStatus.OK, {
      visit_id,
      message: "Visit and associated guest visits have been deleted"
    });
  } catch (error) {
    await t.rollback();
    sendError(res, error.message || "Failed to delete visit", httpStatus.BAD_REQUEST);
  }
});

/**
 * @desc Check-in a guest for a visit
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing check-in details
 */
exports.checkInGuest = catchAsync(async (req, res) => {
  const { guest_id, visit_id } = req.params;
  const { updated_by, check_in_time  } = req.body;
  const t = await sequelize.transaction();

  try {
    // Find guest visit by guest_id and visit_id
    const guestVisit = await GuestVisit.findOne({
      where: { guest_id, visit_id },
      transaction: t
    });

    if (!guestVisit) {
      await t.rollback();
      return sendError(res, "Guest visit association not found", httpStatus.NOT_FOUND);
    }

    // Check if guest is already checked in
    if (guestVisit.guest_status === 1) {
      await t.rollback();
      return sendError(res, "Guest is already checked in", httpStatus.BAD_REQUEST);
    }

    // Generate 6-digit PIN
    const guest_pin = generateOTP(6);
    // Use provided check_in_time or current time as fallback
    const checkinTime = check_in_time ? new Date(check_in_time) : new Date();

    // Update guest visit with check-in details
    const updatedGuestVisit = await guestVisit.update({
      guest_status: 1, // Checked in
      check_in_time: checkinTime,
      guest_pin,
      updated_by
    }, { transaction: t });

    // Create entry in visit_guest_checkin table
    const visitGuestCheckin = await VisitGuestCheckin.create({
      guest_visit_id: guestVisit.guest_visit_id,
      check_in_time: checkinTime,
      updated_by
    }, { transaction: t });

    await t.commit();
    sendSuccess(res, "Guest checked in successfully", httpStatus.OK, {
      guestVisit: updatedGuestVisit,
      checkinRecord: visitGuestCheckin,
      guest_pin
    });
  } catch (error) {
    await t.rollback();
    sendError(res, error.message || "Failed to check in guest", httpStatus.BAD_REQUEST);
  }
});

/**
 * @desc Check-out a guest from a visit
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object containing check-out details
 */
exports.checkOutGuest = catchAsync(async (req, res) => {
  const { guest_id, visit_id } = req.params;
  const { updated_by, check_out_time } = req.body;
  const t = await sequelize.transaction();

  try {
    // Find guest visit by guest_id and visit_id
    const guestVisit = await GuestVisit.findOne({
      where: { guest_id, visit_id },
      transaction: t
    });

    if (!guestVisit) {
      await t.rollback();
      return sendError(res, "Guest visit association not found", httpStatus.NOT_FOUND);
    }

    // Check if guest is checked in
    if (guestVisit.guest_status !== 1) {
      await t.rollback();
      return sendError(res, "Guest is not checked in", httpStatus.BAD_REQUEST);
    }

    // Use provided check_out_time or current time as fallback
    const checkoutTime = check_out_time ? new Date(check_out_time) : new Date();

    // Update guest visit with check-out details
    const updatedGuestVisit = await guestVisit.update({
      guest_status: 2, // Checked out
      check_out_time: checkoutTime,
      updated_by
    }, { transaction: t });

    // Find existing checkin record and update it with checkout time
    const existingCheckinRecord = await VisitGuestCheckin.findOne({
      where: { guest_visit_id: guestVisit.guest_visit_id },
      transaction: t
    });

    let checkinRecord;
    if (existingCheckinRecord) {
      // Update existing record with checkout time
      checkinRecord = await existingCheckinRecord.update({
        check_out_time: checkoutTime,
        updated_by
      }, { transaction: t });
    } else {
      // Create new record if none exists (fallback)
      checkinRecord = await VisitGuestCheckin.create({
        guest_visit_id: guestVisit.guest_visit_id,
        check_in_time: guestVisit.check_in_time,
        check_out_time: checkoutTime,
        updated_by
      }, { transaction: t });
    }

    await t.commit();
    sendSuccess(res, "Guest checked out successfully", httpStatus.OK, {
      guestVisit: updatedGuestVisit,
      checkinRecord
    });
  } catch (error) {
    await t.rollback();
    sendError(res, error.message || "Failed to check out guest", httpStatus.BAD_REQUEST);
  }
});


/**
 * @desc Get all guest details for a visit in a facility, with optional filters
 * @param {Object} req - Express request object
 * @param {Object} res - Express response object
 * @returns {JSON} JSON array containing guest details for the visit
 */
exports.getVisitGuestDetails = catchAsync(async (req, res) => {


  const { facilityId } = req.params;
  const {
    guest_status,
    host_id,
    checkin_date,
    guest_id,
    visitId,
    search = '',
    page = 1,
    limit = 10,
    sortBy,
    sortOrder
  } = req.query;

  // Build where conditions
  const whereConditions = {};

  // Add facility filter (always required)
  whereConditions.facility_id = facilityId;

  // Add optional filters
  if (visitId) {
    whereConditions.visit_id = visitId;
  }

  if (guest_id) {
    whereConditions.guest_id = guest_id;
  }

  if (guest_status !== undefined) {
    whereConditions.guest_status = guest_status;
  }

  if (host_id) {
    whereConditions.host_id = host_id;
  }

  // Add search filter if provided
  if (search) {
    whereConditions[Op.or] = [
      { guest_name: { [Op.like]: `%${search}%` } },
      { host_name: { [Op.like]: `%${search}%` } },
      { facility_name: { [Op.like]: `%${search}%` } }
    ];
  }

  // Add checkin_date filter if provided
  if (checkin_date) {
    const startOfDay = new Date(checkin_date);
    startOfDay.setHours(0, 0, 0, 0);
    const endOfDay = new Date(checkin_date);
    endOfDay.setHours(23, 59, 59, 999);

    whereConditions.check_in_time = {
      [Op.between]: [startOfDay, endOfDay]
    };
  }

  // Set up pagination options
  const paginationOptions = {
    page: parseInt(page, 10),
    limit: parseInt(limit, 10),
    sortBy,
    sortOrder
  };

  // Query the view with pagination
  const result = await paginate(VisitGuestView, { where: whereConditions }, paginationOptions);

  if (!result.data || result.data.length === 0) {
    return sendError(res, "No guests found for this visit", httpStatus.NOT_FOUND);
  }

  sendSuccess(res, "Guest visit details retrieved successfully", httpStatus.OK, result);
});

/**
 * @desc Get all the visits for a host
 * @param {Object} req - Express request object
 * @param {Object} req.query.search - Host name or host ID to search for
 * @param {Object} res - Express response object
 * @returns {JSON} JSON array containing guest details for the visit
 */
exports.getVisits = catchAsync(async (req, res) => {

  const { search, page = 1, limit = 10 } = req.query;

  const sortBy = "visit_created_at", sortOrder = "DESC";

  // Build where conditions for host search
  const whereConditions = {};

  whereConditions[Op.or] = [
    { emp_id: { [Op.iLike]: `%${search}%` } },
    { host_name: { [Op.iLike]: `%${search}%` } }
  ];

  // Set up pagination options
  const paginationOptions = {
    page: parseInt(page, 10),
    limit: parseInt(limit, 10),
    sortBy,
    sortOrder
  };

  // Query the view with distinct visits
  const queryOptions = {
    where: whereConditions,
    attributes: [
      [sequelize.fn('DISTINCT', sequelize.col('visit_id')), 'visit_id'],
      'host_id',
      'host_name',
      'facility_id',
      'visit_created_at'
    ],
    order: [[sortBy, sortOrder]]
  };

  const result = await paginate(VisitGuestView, queryOptions, paginationOptions);

  if (!result.data || result.data.length === 0) {
    return sendError(res, "No visits found for the specified host", httpStatus.NOT_FOUND);
  }

  sendSuccess(res, "Visits retrieved successfully", httpStatus.OK, result);
});
