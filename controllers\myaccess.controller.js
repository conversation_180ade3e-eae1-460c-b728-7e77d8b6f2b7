const { 
  IdentityAccess,
  AccessLevel,
  Facility,
  FacilityAccessLevel,
  Building,
  Floor,
  Room,
  Identity,
  MasterData
} = require("../models");
const httpStatus = require("http-status");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { paginate } = require("../models/plugins/paginate.plugin");
const { Op } = require("sequelize");

/**
 * @desc Get areas owned by the logged-in user
 * @param {Object} req - Express request object
 * @param {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object with owned areas information
 */
exports.getMyOwnedAreas = catchAsync(async (req, res) => {
  const { identity_id } = req.identity;

  if (!identity_id) {
    return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
  }

  // Get the user's EID to find areas they own
  const currentUser = await Identity.findByPk(identity_id, {
    attributes: ['eid']
  });

  if (!currentUser || !currentUser.eid) {
    return sendError(res, "User EID not found", httpStatus.NOT_FOUND);
  }

  // Find all access records where the current user is the owner
  const ownedAreas = await IdentityAccess.findAll({
    where: { 
      owner_eid: currentUser.eid,
      status: 0 // Assuming 0 is active status
    },
    attributes: [
      'identity_access_id',
      'start_date',
      'end_date',
      'status'
    ],
    include: [
      {
        model: AccessLevel,
        as: 'access_level',
        attributes: [
          'access_level_id',
          'name',
          'description',
          'pacs_area_name'
        ],
        include: [
          {
            model: Facility,
            as: 'facility',
            attributes: ['facility_id', 'name']
          },
          {
            model: FacilityAccessLevel,
            as: 'facility_access_level',
            attributes: ['facility_access_level_id'],
            include: [
              {
                model: Building,
                as: 'building',
                attributes: ['building_id', 'name']
              },
              {
                model: Floor,
                as: 'floor',
                attributes: ['floor_id', 'floor_number']
              },
              {
                model: Room,
                as: 'room',
                attributes: ['room_id', 'room_number']
              }
            ]
          }
        ]
      },
      {
        model: MasterData,
        as: 'identity_access_status_name',
        attributes: ['value']
      }
    ],
    order: [['created_at', 'DESC']]
  });

  if (!ownedAreas || ownedAreas.length === 0) {
    return sendError(res, "No owned areas found for the user", httpStatus.NOT_FOUND);
  }

  // Format the response data
  const formattedAreas = ownedAreas.map(area => {
    const facilityAccessLevel = area.access_level?.facility_access_level?.[0];
    
    return {
      access_level_id: area.access_level?.access_level_id,
      area_name: area.access_level?.name,
      pacs_area_name: area.access_level?.pacs_area_name,
      description: area.access_level?.description,
      facility_name: area.access_level?.facility?.name,
      building_name: facilityAccessLevel?.building?.name,
      floor_number: facilityAccessLevel?.floor?.floor_number,
      room_number: facilityAccessLevel?.room?.room_number,
      start_date: area.start_date,
      end_date: area.end_date,
      status: area.identity_access_status_name?.value || 'Unknown'
    };
  });

  sendSuccess(res, "Owned areas retrieved successfully", httpStatus.OK, formattedAreas);
});

/**
 * @desc Get identities that have access to areas owned by the logged-in user
 * @param {Object} req - Express request object
 * @param {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param {Object} req.query - Query parameters for filtering and pagination
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object with accessible identities information
 */
exports.getAccessibleIdentities = catchAsync(async (req, res) => {
  const { identity_id } = req.identity;
  const { access_level_id, page, limit } = req.query;

  if (!identity_id) {
    return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
  }

  // Get the user's EID to find areas they own
  const currentUser = await Identity.findByPk(identity_id, {
    attributes: ['eid']
  });

  if (!currentUser || !currentUser.eid) {
    return sendError(res, "User EID not found", httpStatus.NOT_FOUND);
  }

  // First, get all access levels owned by the current user
  const ownedAccessLevels = await IdentityAccess.findAll({
    where: {
      owner_eid: currentUser.eid,
      status: 0 // Assuming 0 is active status
    },
    attributes: ['access_level_id'],
    include: [
      {
        model: AccessLevel,
        as: 'access_level',
        attributes: ['access_level_id']
      }
    ]
  });

  if (!ownedAccessLevels || ownedAccessLevels.length === 0) {
    return sendError(res, "No owned areas found for the user", httpStatus.NOT_FOUND);
  }

  const ownedAccessLevelIds = ownedAccessLevels.map(area => area.access_level_id);

  // Build where conditions for filtering
  const whereConditions = {
    access_level_id: {
      [Op.in]: ownedAccessLevelIds
    },
    status: 0 // Active access only
  };

  // Add specific access level filter if provided
  if (access_level_id) {
    whereConditions.access_level_id = access_level_id;
  }

  // Pagination options
  const paginationOptions = { page, limit };

  // Query options for finding identities with access to owned areas
  const queryOptions = {
    where: whereConditions,
    attributes: [
      'identity_access_id',
      'start_date',
      'end_date',
      'status'
    ],
    include: [
      {
        model: Identity,
        as: 'identity',
        attributes: [
          'identity_id',
          'first_name',
          'last_name',
          'email',
          'eid',
          'company',
          'job_title'
        ]
      },
      {
        model: AccessLevel,
        as: 'access_level',
        attributes: [
          'access_level_id',
          'name',
          'description'
        ],
        include: [
          {
            model: Facility,
            as: 'facility',
            attributes: ['facility_id', 'name']
          }
        ]
      },
      {
        model: MasterData,
        as: 'identity_access_status_name',
        attributes: ['value']
      }
    ],
    order: [['created_at', 'DESC']]
  };

  const result = await paginate(IdentityAccess, queryOptions, paginationOptions);

  if (!result.data || result.data.length === 0) {
    return sendError(res, "No accessible identities found", httpStatus.NOT_FOUND);
  }

  // Format the response data
  const formattedData = {
    ...result,
    data: result.data.map(access => ({
      identity_id: access.identity?.identity_id,
      first_name: access.identity?.first_name,
      last_name: access.identity?.last_name,
      email: access.identity?.email,
      eid: access.identity?.eid,
      company: access.identity?.company,
      job_title: access.identity?.job_title,
      area_name: access.access_level?.name,
      area_description: access.access_level?.description,
      facility_name: access.access_level?.facility?.name,
      access_start_date: access.start_date,
      access_end_date: access.end_date,
      access_status: access.identity_access_status_name?.value || 'Unknown'
    }))
  };

  sendSuccess(res, "Accessible identities retrieved successfully", httpStatus.OK, formattedData);
});

/**
 * @desc Get people who report directly to the logged-in user
 * @param {Object} req - Express request object
 * @param {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param {Object} req.query - Query parameters for filtering and pagination
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object with direct reports information
 */
exports.getDirectReports = catchAsync(async (req, res) => {
  const { identity_id } = req.identity;
  const { page, limit, search } = req.query;

  if (!identity_id) {
    return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
  }

  // Build where conditions for filtering
  const whereConditions = {
    manager: identity_id,
    status: { [Op.ne]: null } // Exclude null status (assuming null means inactive)
  };

  // Add search functionality
  if (search && search.trim()) {
    whereConditions[Op.or] = [
      { first_name: { [Op.iLike]: `%${search.trim()}%` } },
      { last_name: { [Op.iLike]: `%${search.trim()}%` } },
      { email: { [Op.iLike]: `%${search.trim()}%` } },
      { eid: { [Op.iLike]: `%${search.trim()}%` } }
    ];
  }

  // Pagination options
  const paginationOptions = { page, limit };

  // Query options for finding direct reports
  const queryOptions = {
    where: whereConditions,
    attributes: [
      'identity_id',
      'first_name',
      'last_name',
      'email',
      'eid',
      'job_title',
      'company',
      'organization',
      'start_date',
      'status'
    ],
    include: [
      {
        model: MasterData,
        as: 'identity_status_name',
        attributes: ['value']
      }
    ],
    order: [['first_name', 'ASC'], ['last_name', 'ASC']]
  };

  const result = await paginate(Identity, queryOptions, paginationOptions);

  if (!result.data || result.data.length === 0) {
    return sendError(res, "No direct reports found", httpStatus.NOT_FOUND);
  }

  // Format the response data
  const formattedData = {
    ...result,
    data: result.data.map(person => ({
      identity_id: person.identity_id,
      first_name: person.first_name,
      last_name: person.last_name,
      email: person.email,
      eid: person.eid,
      job_title: person.job_title,
      company: person.company,
      organization: person.organization,
      start_date: person.start_date,
      status: person.identity_status_name?.value || 'Unknown'
    }))
  };

  sendSuccess(res, "Direct reports retrieved successfully", httpStatus.OK, formattedData);
});

/**
 * @desc Get people who report to the logged-in user and their subordinates (hierarchical reporting structure)
 * @param {Object} req - Express request object
 * @param {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param {Object} req.query - Query parameters for filtering and pagination
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object with hierarchical reports information
 */
exports.getHierarchicalReports = catchAsync(async (req, res) => {
  const { identity_id } = req.identity;
  const { page, limit, search, level = 2 } = req.query;

  if (!identity_id) {
    return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
  }

  /**
   * Recursive function to get all subordinates up to a specified level
   * @param {string} managerId - The manager's identity_id
   * @param {number} currentLevel - Current hierarchy level (1 = direct reports)
   * @param {number} maxLevel - Maximum level to traverse
   * @returns {Promise<Array>} Promise that resolves to array of subordinate identity_ids with their levels
   */
  const getSubordinatesRecursively = async (managerId, currentLevel, maxLevel) => {
    if (currentLevel > maxLevel) {
      return [];
    }

    // Get direct reports for this manager
    const directReports = await Identity.findAll({
      where: {
        manager: managerId,
        status: { [Op.ne]: null }
      },
      attributes: ['identity_id', 'first_name', 'last_name']
    });

    let allSubordinates = [];

    // Add direct reports with their level
    for (const report of directReports) {
      allSubordinates.push({
        identity_id: report.identity_id,
        reporting_level: currentLevel,
        manager_id: managerId
      });

      // Recursively get their subordinates if we haven't reached max level
      if (currentLevel < maxLevel) {
        const subSubordinates = await getSubordinatesRecursively(
          report.identity_id,
          currentLevel + 1,
          maxLevel
        );
        allSubordinates = allSubordinates.concat(subSubordinates);
      }
    }

    return allSubordinates;
  };

  // Get all subordinates up to the specified level
  const allSubordinates = await getSubordinatesRecursively(identity_id, 1, level);

  if (!allSubordinates || allSubordinates.length === 0) {
    return sendError(res, "No hierarchical reports found", httpStatus.NOT_FOUND);
  }

  // Extract identity_ids for the main query
  const subordinateIds = allSubordinates.map(sub => sub.identity_id);

  // Build where conditions for filtering
  const whereConditions = {
    identity_id: { [Op.in]: subordinateIds },
    status: { [Op.ne]: null }
  };

  // Add search functionality
  if (search && search.trim()) {
    whereConditions[Op.and] = [
      { identity_id: { [Op.in]: subordinateIds } },
      {
        [Op.or]: [
          { first_name: { [Op.iLike]: `%${search.trim()}%` } },
          { last_name: { [Op.iLike]: `%${search.trim()}%` } },
          { email: { [Op.iLike]: `%${search.trim()}%` } },
          { eid: { [Op.iLike]: `%${search.trim()}%` } }
        ]
      }
    ];
    delete whereConditions.identity_id; // Remove the original condition since it's now in [Op.and]
  }

  // Pagination options
  const paginationOptions = { page, limit };

  // Query options for finding hierarchical reports
  const queryOptions = {
    where: whereConditions,
    attributes: [
      'identity_id',
      'first_name',
      'last_name',
      'email',
      'eid',
      'job_title',
      'company',
      'organization',
      'start_date',
      'status',
      'manager'
    ],
    include: [
      {
        model: MasterData,
        as: 'identity_status_name',
        attributes: ['value']
      },
      {
        model: Identity,
        as: 'manager_identity',
        attributes: ['first_name', 'last_name', 'email'],
        required: false
      }
    ],
    order: [['first_name', 'ASC'], ['last_name', 'ASC']]
  };

  const result = await paginate(Identity, queryOptions, paginationOptions);

  if (!result.data || result.data.length === 0) {
    return sendError(res, "No hierarchical reports found", httpStatus.NOT_FOUND);
  }

  // Create a map for quick lookup of reporting levels
  const levelMap = {};
  allSubordinates.forEach(sub => {
    levelMap[sub.identity_id] = sub.reporting_level;
  });

  // Format the response data
  const formattedData = {
    ...result,
    data: result.data.map(person => ({
      identity_id: person.identity_id,
      first_name: person.first_name,
      last_name: person.last_name,
      email: person.email,
      eid: person.eid,
      job_title: person.job_title,
      company: person.company,
      organization: person.organization,
      start_date: person.start_date,
      status: person.identity_status_name?.value || 'Unknown',
      reporting_level: levelMap[person.identity_id] || 1,
      manager_name: person.manager_identity ?
        `${person.manager_identity.first_name} ${person.manager_identity.last_name}` :
        'Unknown'
    }))
  };

  sendSuccess(res, "Hierarchical reports retrieved successfully", httpStatus.OK, formattedData);
});
