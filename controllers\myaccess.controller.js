const { 
  IdentityAccess,
  AccessLevel,
  Facility,
  FacilityAccessLevel,
  Building,
  Floor,
  Room,
  Identity,
  MasterData
} = require("../models");
const httpStatus = require("http-status");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { paginate } = require("../models/plugins/paginate.plugin");
const { Op } = require("sequelize");

/**
 * @desc Get areas owned by the logged-in user
 * @param {Object} req - Express request object
 * @param {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object with owned areas information
 */
exports.getMyOwnedAreas = catchAsync(async (req, res) => {
  const { identity_id } = req.identity;

  if (!identity_id) {
    return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
  }

  // Get the user's EID to find areas they own
  const currentUser = await Identity.findByPk(identity_id, {
    attributes: ['eid']
  });

  if (!currentUser || !currentUser.eid) {
    return sendError(res, "User EID not found", httpStatus.NOT_FOUND);
  }

  // Find all access records where the current user is the owner
  const ownedAreas = await IdentityAccess.findAll({
    where: { 
      owner_eid: currentUser.eid,
      status: 0 // Assuming 0 is active status
    },
    attributes: [
      'identity_access_id',
      'start_date',
      'end_date',
      'status'
    ],
    include: [
      {
        model: AccessLevel,
        as: 'access_level',
        attributes: [
          'access_level_id',
          'name',
          'description',
          'pacs_area_name'
        ],
        include: [
          {
            model: Facility,
            as: 'facility',
            attributes: ['facility_id', 'name']
          },
          {
            model: FacilityAccessLevel,
            as: 'facility_access_level',
            attributes: ['facility_access_level_id'],
            include: [
              {
                model: Building,
                as: 'building',
                attributes: ['building_id', 'name']
              },
              {
                model: Floor,
                as: 'floor',
                attributes: ['floor_id', 'floor_number']
              },
              {
                model: Room,
                as: 'room',
                attributes: ['room_id', 'room_number']
              }
            ]
          }
        ]
      },
      {
        model: MasterData,
        as: 'identity_access_status_name',
        attributes: ['value']
      }
    ],
    order: [['created_at', 'DESC']]
  });

  if (!ownedAreas || ownedAreas.length === 0) {
    return sendError(res, "No owned areas found for the user", httpStatus.NOT_FOUND);
  }

  // Format the response data
  const formattedAreas = ownedAreas.map(area => {
    const facilityAccessLevel = area.access_level?.facility_access_level?.[0];
    
    return {
      access_level_id: area.access_level?.access_level_id,
      area_name: area.access_level?.name,
      pacs_area_name: area.access_level?.pacs_area_name,
      description: area.access_level?.description,
      facility_name: area.access_level?.facility?.name,
      building_name: facilityAccessLevel?.building?.name,
      floor_number: facilityAccessLevel?.floor?.floor_number,
      room_number: facilityAccessLevel?.room?.room_number,
      start_date: area.start_date,
      end_date: area.end_date,
      status: area.identity_access_status_name?.value || 'Unknown'
    };
  });

  sendSuccess(res, "Owned areas retrieved successfully", httpStatus.OK, formattedAreas);
});

/**
 * @desc Get identities that have access to areas owned by the logged-in user
 * @param {Object} req - Express request object
 * @param {Object} req.identity - Contains the current user's identity_id from JWT token
 * @param {Object} req.query - Query parameters for filtering and pagination
 * @param {Object} res - Express response object
 * @returns {JSON} JSON object with accessible identities information
 */
exports.getAccessibleIdentities = catchAsync(async (req, res) => {
  const { identity_id } = req.identity;
  const { access_level_id, page, limit } = req.query;

  if (!identity_id) {
    return sendError(res, "User identity not found in token", httpStatus.UNAUTHORIZED);
  }

  // Get the user's EID to find areas they own
  const currentUser = await Identity.findByPk(identity_id, {
    attributes: ['eid']
  });

  if (!currentUser || !currentUser.eid) {
    return sendError(res, "User EID not found", httpStatus.NOT_FOUND);
  }

  // First, get all access levels owned by the current user
  const ownedAccessLevels = await IdentityAccess.findAll({
    where: {
      owner_eid: currentUser.eid,
      status: 0 // Assuming 0 is active status
    },
    attributes: ['access_level_id'],
    include: [
      {
        model: AccessLevel,
        as: 'access_level',
        attributes: ['access_level_id']
      }
    ]
  });

  if (!ownedAccessLevels || ownedAccessLevels.length === 0) {
    return sendError(res, "No owned areas found for the user", httpStatus.NOT_FOUND);
  }

  const ownedAccessLevelIds = ownedAccessLevels.map(area => area.access_level_id);

  // Build where conditions for filtering
  const whereConditions = {
    access_level_id: {
      [Op.in]: ownedAccessLevelIds
    },
    status: 0 // Active access only
  };

  // Add specific access level filter if provided
  if (access_level_id) {
    whereConditions.access_level_id = access_level_id;
  }

  // Pagination options
  const paginationOptions = { page, limit };

  // Query options for finding identities with access to owned areas
  const queryOptions = {
    where: whereConditions,
    attributes: [
      'identity_access_id',
      'start_date',
      'end_date',
      'status'
    ],
    include: [
      {
        model: Identity,
        as: 'identity',
        attributes: [
          'identity_id',
          'first_name',
          'last_name',
          'email',
          'eid',
          'company',
          'job_title'
        ]
      },
      {
        model: AccessLevel,
        as: 'access_level',
        attributes: [
          'access_level_id',
          'name',
          'description'
        ],
        include: [
          {
            model: Facility,
            as: 'facility',
            attributes: ['facility_id', 'name']
          }
        ]
      },
      {
        model: MasterData,
        as: 'identity_access_status_name',
        attributes: ['value']
      }
    ],
    order: [['created_at', 'DESC']]
  };

  const result = await paginate(IdentityAccess, queryOptions, paginationOptions);

  if (!result.data || result.data.length === 0) {
    return sendError(res, "No accessible identities found", httpStatus.NOT_FOUND);
  }

  // Format the response data
  const formattedData = {
    ...result,
    data: result.data.map(access => ({
      identity_id: access.identity?.identity_id,
      first_name: access.identity?.first_name,
      last_name: access.identity?.last_name,
      email: access.identity?.email,
      eid: access.identity?.eid,
      company: access.identity?.company,
      job_title: access.identity?.job_title,
      area_name: access.access_level?.name,
      area_description: access.access_level?.description,
      facility_name: access.access_level?.facility?.name,
      access_start_date: access.start_date,
      access_end_date: access.end_date,
      access_status: access.identity_access_status_name?.value || 'Unknown'
    }))
  };

  sendSuccess(res, "Accessible identities retrieved successfully", httpStatus.OK, formattedData);
});
