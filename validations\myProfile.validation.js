const Joi = require("joi");
const { existsMasterData, exists } = require("./custom.validation");

module.exports = {
  createCardRequest: {
    body: Joi.object().keys({
      card_number: Joi.string().required(),
      card_format: Joi.number().integer().required(),
      facility_code: Joi.number().integer().optional().allow(null),
      pin: Joi.number().integer().optional().allow(null),
      template: Joi.number().integer().external(existsMasterData("card_template")).required(),
      active_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      deactive_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }),
      reason: Joi.string().optional().allow(""),
      shipping_required: Joi.number().integer().external(existsMasterData("shipping_required")).required(),
      status: Joi.number().integer().external(existsMasterData("card_status")).required(),
      pick_up_facility_id: Joi.string().uuid().optional().allow(""),
      
      // Shipping address fields - required when shipping_required is 1
      ship_to: Joi.when('shipping_required', {
        is: 1,
        then: Joi.string().required(),
        otherwise: Joi.string().optional().allow("")
      }),
      ship_to_name: Joi.when('shipping_required', {
        is: 1,
        then: Joi.string().required(),
        otherwise: Joi.string().optional().allow("")
      }),
      address_line_1: Joi.when('shipping_required', {
        is: 1,
        then: Joi.string().required(),
        otherwise: Joi.string().optional().allow("")
      }),
      address_line_2: Joi.when('shipping_required', {
        is: 1,
        then: Joi.string().optional().allow(""),
        otherwise: Joi.string().optional().allow("")
      }),
      country_id: Joi.when('shipping_required', {
        is: 1,
        then: Joi.string().uuid().external(exists("Country", "country_id")).required(),
        otherwise: Joi.string().uuid().optional().allow("")
      }),
      state_id: Joi.when('shipping_required', {
        is: 1,
        then: Joi.string().uuid().external(exists("State", "state_id")).required(),
        otherwise: Joi.string().uuid().optional().allow("")
      }),
      zip_code: Joi.when('shipping_required', {
        is: 1,
        then: Joi.string().required(),
        otherwise: Joi.string().optional().allow("")
      }),
      mobile_phone: Joi.when('shipping_required', {
        is: 1,
        then: Joi.string().required(),
        otherwise: Joi.string().optional().allow("")
      }),
    }),
  },

  createVehicle: {
    body: Joi.object().keys({
      plate_number: Joi.string().required().max(20).description("Vehicle license plate number"),
      issued_by: Joi.string().optional().allow("").max(100).description("Authority that issued the plate"),
      vin: Joi.string().optional().allow("").length(17).description("Vehicle Identification Number (17 characters)"),
      year: Joi.number().integer().min(1900).max(new Date().getFullYear() + 1).optional().description("Manufacturing year"),
      make: Joi.string().optional().allow("").max(100).description("Vehicle manufacturer"),
      model: Joi.string().optional().allow("").max(100).description("Vehicle model"),
      color: Joi.string().optional().allow("").max(50).description("Vehicle color"),
      uploaded_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }).description("Date when vehicle information was uploaded")
    })
  },

  createDelegation: {
    body: Joi.object().keys({
      identity_id: Joi.string().uuid().required().description("UUID of the identity to delegate to"),
      task_to_delegate: Joi.string().required().max(1000).description("Description of the task to delegate"),
      start_date: Joi.date().required().description("Start date of the delegation"),
      end_date: Joi.date().optional().allow("").custom((value) => {
        if (value === "") return null;
        return value;
      }).description("End date of the delegation (optional)"),
      name: Joi.string().optional().allow("").max(200).description("Name of the delegate (optional, can be auto-filled from identity)"),
      eid: Joi.string().optional().allow("").max(50).description("Employee ID of the delegate (optional)")
    })
  }
}
