const Joi = require("joi");

/**
 * Schema for getting direct reports of the logged-in user.
 */
const getDirectReports = {
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(10),
    search: Joi.string().optional().allow('')
  })
};

/**
 * Schema for getting hierarchical reports of the logged-in user.
 */
const getHierarchicalReports = {
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(10),
    search: Joi.string().optional().allow(''),
    level: Joi.number().integer().min(1).max(5).optional().default(2)
  })
};

module.exports = {
  getDirectReports,
  getHierarchicalReports
};
