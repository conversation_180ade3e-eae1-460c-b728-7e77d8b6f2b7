const express = require("express");
const validate = require("../middlewares/validate");
const auth = require("../middlewares/auth");
const { MyaccessValidation } = require("../validations");
const { MyaccessController } = require("../controllers");

const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: MyAccess
 *   description: User area access management
 */

/**
 * @swagger
 * /myaccess/owned-areas:
 *   get:
 *     summary: Get areas owned by the logged-in user
 *     tags: [MyAccess]
 *     security:
 *       - bearerAuth: []
 *     responses:
 *       200:
 *         description: Owned areas retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Owned areas retrieved successfully"
 *                 data:
 *                   type: array
 *                   items:
 *                     type: object
 *                     properties:
 *                       access_level_id:
 *                         type: string
 *                         format: uuid
 *                         example: "123e4567-e89b-12d3-a456-************"
 *                       area_name:
 *                         type: string
 *                         example: "Main Entrance"
 *                       description:
 *                         type: string
 *                         example: "Access level for main entrance"
 *                       facility_name:
 *                         type: string
 *                         example: "Central Hospital"
 *                       building_name:
 *                         type: string
 *                         example: "Main Building"
 *                       floor_number:
 *                         type: integer
 *                         example: 1
 *                       room_number:
 *                         type: string
 *                         example: "101A"
 *                       start_date:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-01-01T00:00:00.000Z"
 *                       end_date:
 *                         type: string
 *                         format: date-time
 *                         example: "2024-12-31T23:59:59.000Z"
 *                       status:
 *                         type: string
 *                         example: "Active"
 *       404:
 *         description: No owned areas found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/owned-areas",
  auth("view_my_access"),
  validate(MyaccessValidation.getMyOwnedAreas),
  MyaccessController.getMyOwnedAreas
);

/**
 * @swagger
 * /myaccess/accessible-identities:
 *   get:
 *     summary: Get identities that have access to areas owned by the logged-in user
 *     tags: [MyAccess]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: access_level_id
 *         required: false
 *         schema:
 *           type: string
 *           format: uuid
 *         description: Filter by specific access level ID
 *       - in: query
 *         name: page
 *         required: false
 *         schema:
 *           type: integer
 *           default: 1
 *         description: Page number for pagination
 *       - in: query
 *         name: limit
 *         required: false
 *         schema:
 *           type: integer
 *           default: 10
 *         description: Number of items per page
 *     responses:
 *       200:
 *         description: Accessible identities retrieved successfully
 *         content:
 *           application/json:
 *             schema:
 *               type: object
 *               properties:
 *                 status:
 *                   type: boolean
 *                   example: true
 *                 message:
 *                   type: string
 *                   example: "Accessible identities retrieved successfully"
 *                 data:
 *                   type: object
 *                   properties:
 *                     totalItems:
 *                       type: integer
 *                       example: 25
 *                     totalPages:
 *                       type: integer
 *                       example: 3
 *                     currentPage:
 *                       type: integer
 *                       example: 1
 *                     data:
 *                       type: array
 *                       items:
 *                         type: object
 *                         properties:
 *                           identity_id:
 *                             type: string
 *                             format: uuid
 *                             example: "456e7890-e89b-12d3-a456-************"
 *                           first_name:
 *                             type: string
 *                             example: "John"
 *                           last_name:
 *                             type: string
 *                             example: "Doe"
 *                           email:
 *                             type: string
 *                             example: "<EMAIL>"
 *                           eid:
 *                             type: string
 *                             example: "EMP001"
 *                           area_name:
 *                             type: string
 *                             example: "Main Entrance"
 *                           facility_name:
 *                             type: string
 *                             example: "Central Hospital"
 *                           access_start_date:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-01-01T00:00:00.000Z"
 *                           access_end_date:
 *                             type: string
 *                             format: date-time
 *                             example: "2024-12-31T23:59:59.000Z"
 *                           access_status:
 *                             type: string
 *                             example: "Active"
 *       404:
 *         description: No accessible identities found
 *       401:
 *         description: Unauthorized
 */
router.get(
  "/accessible-identities",
  auth("view_my_access"),
  validate(MyaccessValidation.getAccessibleIdentities),
  MyaccessController.getAccessibleIdentities
);

module.exports = router;
