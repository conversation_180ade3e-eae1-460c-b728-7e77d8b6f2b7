const { Room, Floor, Facility, Building, MasterData } = require("../models");
const { paginate } = require("../models/plugins/paginate.plugin");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { status: httpStatus } = require("http-status");

/**
 * Get all rooms for a specific floor (paginated).
 *
 * @async
 * @function index
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the facilityId.
 * @param {Object} req.query - Query parameters for pagination (page and limit).
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a paginated response with room data, including floor details.
 */
exports.index = catchAsync(async (req, res, next) => {
  const { facilityId } = req.params;
  const { page, limit } = req.query;
  const paginationOptions = { page, limit };

  const queryOptions = {
    order: [['updatedAt', 'DESC']],
    where: { facility_id: facilityId },
    include: [
      {
        model: Floor,
        as: "floor",
        attributes: ["floor_number"],
      },
      {
        model: Building,
        as: "building",
        attributes: ["name", "building_code"],
      },
      {
        model: Facility,
        as: "facility",
        attributes: ["name"],
      },
      {
        model: MasterData,
        as: "room_status_name",
        attributes: ["key", "value"],
      },
    ],
  };

  const result = await paginate(Room, queryOptions, paginationOptions);
  sendSuccess(res, "Rooms retrieved successfully", httpStatus.OK, result);
});

/**
 * Get all rooms for a specific floor (without pagination).
 *
 * @async
 * @function fetch
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the floorId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a response with an array of room data (only id and name).
 */
exports.fetch = catchAsync(async (req, res, next) => {
  const { floorId } = req.params;

  const rooms = await Room.findAll({
    where: { floor_id: floorId },
    attributes: ["room_id", "room_number"], // Selecting only the room's id and name
  });

  sendSuccess(res, "Rooms fetched successfully", httpStatus.OK, { data: rooms });
});


/**
 * Get a single room by its ID for a specific facility.
 *
 * @async
 * @function show
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and roomId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the room details with floor information or a 404 error if not found.
 */
exports.show = catchAsync(async (req, res, next) => {
  const { facilityId, roomId } = req.params;
  const room = await Room.findOne({
    where: { room_id: roomId, facility_id: facilityId },
    include: [
      {
        model: Floor,
        as: "floor",
        attributes: ["floor_number"],
      },
      {
        model: Building,
        as: "building",
        attributes: ["name"],
      },
      {
        model: Facility,
        as: "facility",
        attributes: ["name"], // include only the facility's name
      },
      {
        model: MasterData,
        as: "room_status_name",
        attributes: ["key", "value"],
      },
    ],
  });
  if (!room) {
    return sendError(res, "Room not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Room retrieved successfully", httpStatus.OK, room);
});

/**
 * Create a new room under a specific facility.
 * Uses the entire validated request body.
 *
 * @async
 * @function create
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the facilityId.
 * @param {Object} req.body - Contains the room details.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the created room data including floor details.
 */
exports.create = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId } = req.params;
  const { floor_id, building_id } = req.body;

  if (floor_id) {
    // Verify that the floor exists and belongs to a building in the given facility.
    const floor = await Floor.findOne({
      where: { floor_id },
      where: { facility_id: facilityId },
      include: [
        {
          model: Building,
          as: "building",
          attributes: ["name", "facility_id"],
          where: { facility_id: facilityId },
          where: { building_id },
        },
      ],
    });
    if (!floor) {
      return sendError(
        res,
        "Floor not found for the given facility/building",
        httpStatus.NOT_FOUND
      );
    }
  }

  // Merge facility_id into the request body data
  const roomData = { ...req.body, facility_id: facilityId };

  const room = await Room.create(roomData, { transaction });

  // Retrieve floor details to include in the response.
  const floor = await Floor.findByPk(facilityId, {
    attributes: ["floor_number"],
  });

  sendSuccess(res, "Room created successfully", httpStatus.CREATED, {
    ...room.dataValues,
    floor: floor ? { floor_number: facility.floor_number } : null,
  });
});

/**
 * Update room details.
 * Uses the whole validated request body.
 *
 * @async
 * @function update
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and roomId.
 * @param {Object} req.body - Contains room fields to update.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response or a 404 error if the room is not found.
 */
exports.update = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, roomId } = req.params;
  const { floor_id, building_id } = req.body;

  // Find the existing room without filtering by building_id
  const room = await Room.findOne({
    where: {
      room_id: roomId,
      facility_id: facilityId,
    },
  });

  if (!room) return sendError(res, "Room not found", httpStatus.NOT_FOUND);

  // Validate if the new floor_id exists under the new building_id
  if (floor_id && building_id) {
    const floorExists = await Floor.findOne({
      where: {
        floor_id,
        building_id,
        facility_id: facilityId,
      },
    });

    if (!floorExists) {
      return sendError(res, "Invalid floor for the specified building", httpStatus.BAD_REQUEST);
    }
  }

  // Perform the update
  const [updated] = await Room.update(req.body, {
    where: { room_id: roomId, facility_id: facilityId },
    transaction,
  });

  if (!updated) {
    return sendError(res, "Failed to update room", httpStatus.BAD_REQUEST);
  }

  sendSuccess(res, "Room updated successfully", httpStatus.OK);
});

/**
 * Update room status.
 *
 * @async
 * @function status
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and roomId.
 * @param {Object} req.body - Contains the new status.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a response confirming the status update or a 404 error if the room is not found.
 */
exports.status = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, roomId } = req.params;

  const [updated] = await Room.update(req.body, {
    where: { room_id: roomId, facility_id: facilityId },
    transaction,
  });

  if (!updated) {
    return sendError(res, "Room not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Room status updated successfully", httpStatus.OK);
});

/**
 * Delete a room by its ID for a specific facility.
 *
 * @async
 * @function remove
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and roomId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response confirming deletion or a 404 error if not found.
 */
exports.remove = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, roomId } = req.params;

  // Find the room and verify it belongs to the specified facility
  const room = await Room.findOne({
    where: { room_id: roomId, facility_id: facilityId },
    transaction,
  });

  if (!room) {
    return sendError(res, "Room not found for the given facility", httpStatus.NOT_FOUND);
  }

  // Delete the room
  await room.destroy({ transaction });
  sendSuccess(res, "Room deleted successfully", httpStatus.OK, { room_id: roomId });
});
