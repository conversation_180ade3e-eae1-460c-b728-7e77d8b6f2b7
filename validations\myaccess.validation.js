const Joi = require("joi");

/**
 * Schema for getting owned areas by the logged-in user.
 * No parameters needed - uses identity from <PERSON>W<PERSON> token
 */
const getMyOwnedAreas = {
  // No parameters needed - uses identity from JW<PERSON> token
};

/**
 * Schema for getting identities that have access to areas owned by the logged-in user.
 */
const getAccessibleIdentities = {
  query: Joi.object().keys({
    access_level_id: Joi.string().uuid().optional(),
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(10)
  })
};

/**
 * Schema for getting direct reports of the logged-in user.
 */
const getDirectReports = {
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(10),
    search: Joi.string().optional().allow('')
  })
};

/**
 * Schema for getting hierarchical reports of the logged-in user.
 */
const getHierarchicalReports = {
  query: Joi.object().keys({
    page: Joi.number().integer().min(1).optional().default(1),
    limit: Joi.number().integer().min(1).max(100).optional().default(10),
    search: Joi.string().optional().allow(''),
    level: Joi.number().integer().min(1).max(5).optional().default(2)
  })
};

module.exports = {
  getMyOwnedAreas,
  getAccessibleIdentities,
  getDirectReports,
  getHierarchicalReports
};
