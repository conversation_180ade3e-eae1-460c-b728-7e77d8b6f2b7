const express = require("express");
const auth = require("../middlewares/auth");
const validate = require("../middlewares/validate");
const { ProfileController } = require("../controllers");
const { ProfileValidation } = require("../validations");
const router = express.Router();

/**
 * @swagger
 * tags:
 *   name: Profile
 *   description: User profile management
 */



/**
 * @swagger
 * /profile/language:
 *   put:
 *     summary: Update user language preference
 *     security:
 *       - bearerAuth: []
 *     tags: [Profile]
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               language_id:
 *                 type: string
 *                 description: The ID of the language to set as preference.
 *     responses:
 *       200:
 *         description: Language preference updated successfully.
 *         content:
 *           application/json:
 *             example:
 *               message: "Language preference updated successfully."
 */
router.put(
  "/language",
  auth(),
  validate(ProfileValidation.language),
  ProfileController.language
);

module.exports = router;
