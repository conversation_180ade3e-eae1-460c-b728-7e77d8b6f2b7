const { Building, Facility, MasterData } = require("../models");
const { paginate } = require("../models/plugins/paginate.plugin");
const { sendSuccess, sendError, catchAsync } = require("../helpers/api.helper");
const { status: httpStatus } = require("http-status");

/**
 * Get all buildings for a specific facility (paginated).
 *
 * @async
 * @function index
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the facilityId.
 * @param {Object} req.query - Query parameters for pagination (page and limit).
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a paginated response with building data, including facility name and master data info.
 */
exports.index = catchAsync(async (req, res, next) => {
  const { facilityId } = req.params;
  const { page, limit } = req.query;
  const paginationOptions = { page, limit };

  const queryOptions = {
    order: [['updatedAt', 'DESC']],
    where: { facility_id: facilityId },
    include: [
      { model: Facility, as: "facility", attributes: ["name"] },
      { model: MasterData, as: "building_status_name", attributes: ["key", "value"] },
      { model: MasterData, as: "building_type_name", attributes: ["key", "value"] },
      { model: MasterData, as: "building_occupancy_type_name", attributes: ["key", "value"] },
    ],
  };

  const result = await paginate(Building, queryOptions, paginationOptions);
  sendSuccess(res, "Buildings retrieved successfully", httpStatus.OK, result);
});

/**
 * Get all buildings for a specific facility (without pagination).
 *
 * @async
 * @function fetchBuildings
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the facilityId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a response with an array of building_id and name.
 */
exports.fetch = catchAsync(async (req, res, next) => {
  const { facilityId } = req.params;

  const buildings = await Building.findAll({
    where: { facility_id: facilityId },
    attributes: ["building_id", "name", "building_code"], // Selecting only id and name
  });

  sendSuccess(res, "Buildings fetched successfully", httpStatus.OK, { data: buildings });
});


/**
 * Get a single building by its ID for a specific facility.
 *
 * @async
 * @function show
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and buildingId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the building details with facility name and master data info or a 404 error if not found.
 */
exports.show = catchAsync(async (req, res, next) => {
  const { facilityId, buildingId } = req.params;
  const building = await Building.findOne({
    where: { building_id: buildingId, facility_id: facilityId },
    include: [
      { model: Facility, as: "facility", attributes: ["name"] },
      { model: MasterData, as: "building_status_name", attributes: ["key", "value"] },
      { model: MasterData, as: "building_type_name", attributes: ["key", "value"] },
      { model: MasterData, as: "building_occupancy_type_name", attributes: ["key", "value"] },
    ],
  });

  if (!building) {
    return sendError(res, "Building not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Building retrieved successfully", httpStatus.OK, building);
});

/**
 * Create a new building under a specific facility.
 *
 * @async
 * @function create
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains the facilityId.
 * @param {Object} req.body - Contains the building details.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends the created building data including facility name and master data info.
 */
exports.create = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId } = req.params;

  // Merge facility_id with the request body
  const buildingData = { ...req.body, facility_id: facilityId };

  const building = await Building.create(buildingData, { transaction });

  // Re-fetch the newly created building including associations
  const createdBuilding = await Building.findOne({
    where: { building_id: building.building_id },
    include: [
      { model: Facility, as: "facility", attributes: ["name"] },
      { model: MasterData, as: "building_status_name", attributes: ["key", "value"] },
      { model: MasterData, as: "building_type_name", attributes: ["key", "value"] },
      { model: MasterData, as: "building_occupancy_type_name", attributes: ["key", "value"] },
    ],
    transaction,
  });

  sendSuccess(res, "Building created successfully", httpStatus.CREATED, createdBuilding);
});

/**
 * Update building details.
 *
 * @async
 * @function update
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and buildingId.
 * @param {Object} req.body - Contains building fields to update.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response or a 404 error if the building is not found.
 */
exports.update = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, buildingId } = req.params;

  const [updated] = await Building.update(req.body, {
    where: { building_id: buildingId, facility_id: facilityId },
    transaction,
  });

  if (!updated) {
    return sendError(res, "Building not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Building updated successfully", httpStatus.OK);
});

/**
 * Update building status.
 *
 * @async
 * @function status
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and buildingId.
 * @param {Object} req.body - Contains the new status.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a response confirming the status update or a 404 error if the building is not found.
 */
exports.status = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, buildingId } = req.params;

  const [updated] = await Building.update(req.body, {
    where: { building_id: buildingId, facility_id: facilityId },
    transaction,
  });

  if (!updated) {
    return sendError(res, "Building not found", httpStatus.NOT_FOUND);
  }
  sendSuccess(res, "Building status updated successfully", httpStatus.OK);
});

/**
 * Delete a building by its ID for a specific facility.
 *
 * @async
 * @function remove
 * @param {Object} req - Express request object.
 * @param {Object} req.params - Contains facilityId and buildingId.
 * @param {Object} res - Express response object.
 * @returns {Promise<void>} Sends a success response confirming deletion or a 404 error if not found.
 */
exports.remove = catchAsync(async (req, res, next) => {
  const transaction = req.transaction;
  const { facilityId, buildingId } = req.params;

  // Find the building and verify it belongs to the specified facility
  const building = await Building.findOne({
    where: { building_id: buildingId, facility_id: facilityId },
    transaction,
  });

  if (!building) {
    return sendError(res, "Building not found for the given facility", httpStatus.NOT_FOUND);
  }

  // Delete the building
  await building.destroy({ transaction });
  sendSuccess(res, "Building deleted successfully", httpStatus.OK, { building_id: buildingId });
});
